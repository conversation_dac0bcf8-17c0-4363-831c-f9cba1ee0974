const http = require('http');
const url = require('url');

const PORT = 3000;

// Simple in-memory user store for demo
const users = [
  { id: 1, email: '<EMAIL>', password: 'admin123', firstName: 'Admin', lastName: 'User' },
  { id: 2, email: '<EMAIL>', password: 'user123', firstName: 'Test', lastName: 'User' }
];

// Simple CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': 'http://localhost:5173',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Credentials': 'true'
};

// Simple JWT-like token generation (not secure, just for demo)
function generateToken(user) {
  return Buffer.from(JSON.stringify({ id: user.id, email: user.email })).toString('base64');
}

function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      console.log('Raw body:', body);
      try {
        const parsed = JSON.parse(body);
        console.log('Parsed body:', parsed);
        resolve(parsed);
      } catch (err) {
        console.log('JSON parse error:', err.message);
        resolve({});
      }
    });
    req.on('error', reject);
  });
}

const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Set CORS headers
  Object.keys(corsHeaders).forEach(key => {
    res.setHeader(key, corsHeaders[key]);
  });

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Set content type
  res.setHeader('Content-Type', 'application/json');

  try {
    // Login endpoint
    if (path === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;
      console.log('Login attempt:', { email, password });

      const user = users.find(u => u.email === email && u.password === password);
      
      if (user) {
        const token = generateToken(user);
        res.writeHead(200);
        res.end(JSON.stringify({
          status: 'success',
          message: 'Login successful',
          data: {
            user: {
              id: user.id,
              email: user.email,
              firstName: user.firstName,
              lastName: user.lastName
            },
            token: token
          }
        }));
      } else {
        res.writeHead(401);
        res.end(JSON.stringify({
          status: 'fail',
          message: 'Invalid email or password'
        }));
      }
      return;
    }

    // Register endpoint
    if (path === '/api/auth/register' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password, firstName, lastName } = body;

      // Check if user exists
      const existingUser = users.find(u => u.email === email);
      if (existingUser) {
        res.writeHead(400);
        res.end(JSON.stringify({
          status: 'fail',
          message: 'User with this email already exists'
        }));
        return;
      }

      // Create new user
      const newUser = {
        id: users.length + 1,
        email,
        password,
        firstName,
        lastName
      };
      users.push(newUser);

      const token = generateToken(newUser);
      res.writeHead(201);
      res.end(JSON.stringify({
        status: 'success',
        message: 'Registration successful',
        data: {
          user: {
            id: newUser.id,
            email: newUser.email,
            firstName: newUser.firstName,
            lastName: newUser.lastName
          },
          token: token
        }
      }));
      return;
    }

    // Books endpoint (mock data)
    if (path === '/api/books' && method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        status: 'success',
        data: {
          books: [
            {
              id: 1,
              title: 'Sample PDF Book',
              author: 'Test Author',
              category: 'Technology',
              description: 'A sample book for testing',
              cover_url: '/uploads/covers/sample.jpg',
              page_count: 100,
              file_size: 1024000,
              created_at: new Date().toISOString()
            }
          ]
        }
      }));
      return;
    }

    // Health check
    if (path === '/health' && method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        status: 'success',
        message: 'Server is running',
        timestamp: new Date().toISOString()
      }));
      return;
    }

    // API docs
    if (path === '/api/docs' && method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        message: 'PDF Reader API Documentation',
        version: '1.0.0',
        endpoints: {
          'POST /api/auth/login': 'User login',
          'POST /api/auth/register': 'User registration',
          'GET /api/books': 'List books',
          'GET /health': 'Health check'
        }
      }));
      return;
    }

    // 404 for other routes
    res.writeHead(404);
    res.end(JSON.stringify({
      status: 'fail',
      message: 'Route not found'
    }));

  } catch (error) {
    console.error('Server error:', error);
    res.writeHead(500);
    res.end(JSON.stringify({
      status: 'error',
      message: 'Internal server error'
    }));
  }
});

server.listen(PORT, () => {
  console.log(`Simple PDF Reader Backend running on http://localhost:${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`API docs: http://localhost:${PORT}/api/docs`);
  console.log('');
  console.log('Test credentials:');
  console.log('Email: <EMAIL>, Password: admin123');
  console.log('Email: <EMAIL>, Password: user123');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});
